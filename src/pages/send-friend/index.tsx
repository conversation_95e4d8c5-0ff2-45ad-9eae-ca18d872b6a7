import { Popup, Input, Radio, Notify } from '@taroify/core'
import { useState, useEffect } from 'react'
import { useAsyncFn } from 'react-use'
import shareBg from '@/assets/images/sned-friend/shareBg.png'
import useObjState from '@/hooks/useObjState'
import dayjs from 'dayjs'
import Taro, { useRouter, useShareAppMessage } from '@tarojs/taro'
import SelectConpou from './components/selectConpou'
import { receiveList, transferCoupon } from '../../api/coupon'

import './index.scss'

const Index = () => {
  const router = useRouter()
  const { couponId } = router.params

  const [open, setOpen] = useState(false)
  const [shartVisible, setShartVisible] = useState(false)

  const [radioValue, setRadioValue] = useState('3')
  const [transferNum, setTransferNum] = useState('')
  const [limitReceiveNum, setLimitReceiveNum] = useState('')
  const [message, setMessage] = useState('')

  const activeProductGroupId = useObjState(1)
  const pageNum = useObjState('1')
  const pageSize = useObjState('10')
  const [nouponList, setNouponList] = useState([])

  const [btnText, setBtnText] = useState('立即赠送')

  // 赠送后接口返回的code分享码
  const [shareCode, setShareCode] = useState()

  // 默认展示的优惠券
  const defaultShowNoupon = useObjState<any>([])
  // const [defaultNouponId, setDefaultNouponId] = useState(couponId)

  const [getOrderList, getOrderListFetch] = useAsyncFn(async () => {
    const params: { pageNum: string; pageSize: string } = {
      pageNum: pageNum.val,
      pageSize: pageSize.val
    }
    const res = await receiveList(params.pageNum, params.pageSize)
    setNouponList(res.data.list)
    const defaultNouponId = defaultShowNoupon.val.id || couponId
    let currentCouponData = res.data.list.find((item) => item.id == defaultNouponId)

    if (currentCouponData.totalNum == 0) {
      defaultShowNoupon.set(res.data.list[0])
    } else {
      defaultShowNoupon.set(currentCouponData)
    }

    return res.data
  }, [activeProductGroupId.val, pageNum.val, pageSize.val])

  useEffect(() => {
    getOrderListFetch()
  }, [activeProductGroupId.val, pageNum.val, pageSize.val])

  // 子组件更换优惠券
  const updateSelectConpouItem = (conputItem) => {
    defaultShowNoupon.set(conputItem)
    setOpen(false)
    setBtnText('立即赠送')

    setRadioValue('3')
    setTransferNum('')
    setLimitReceiveNum('')
    setMessage('')
  }

  // 赠送优惠券数量
  const setTransferNumFn = (e) => {
    const num = Number(e.detail.value || 0)
    const fixTotal = Number(defaultShowNoupon.val.totalNum || 0)
    if (num < 0) return
    if (num > fixTotal) return Notify.open({ type: 'danger', message: '赠送代金券数量，不可超过总数' })
    setTransferNum(`${num}`)
  }

  // 每个领取多张赠送优惠券数量
  const setLimitNumFn = (e) => {
    const num = Number(e.detail.value || 0)
    const fixTransferNum = Number(transferNum || 0)
    if (num < 0) return
    if (transferNum && num > fixTransferNum) return Notify.open({ type: 'danger', message: '每人领取代金券数量，不可超过总数' })
    setLimitReceiveNum(`${num}`)
  }

  // 立即赠送
  const sendNoupon = async () => {
    let limitReceiveVal
    if (radioValue == '1') {
      limitReceiveVal = 1
    } else if (radioValue == '3') {
      limitReceiveVal = transferNum
    } else if (radioValue == '2') {
      limitReceiveVal = limitReceiveNum
    }
    const data = {
      couponId: defaultShowNoupon.val.id, // 优惠券id
      transferNum: Number(transferNum), // 转赠优惠券数量
      limitReceiveNum: Number(limitReceiveVal), // 限制领取数量
      message: message // 留言
    }
    if (btnText == '立即赠送') {
      const res = await transferCoupon(data)
      setShareCode(res.data)
    }

    setShartVisible(true)
  }

  // 分享
  useShareAppMessage(() => {
    getOrderListFetch().then((res) => {
      // const defaultShowFn = defaultShowNoupon.get()
      // console.log('defaultShowNoupon', defaultShowNoupon)
      // // 当分享完成后，如果当前优惠券数量 为0，则读取优惠券列表第一个
      // if (defaultShowFn.totalNum == 0) {
      //   defaultShowNoupon.set(res[0])
      //   console.log('xxxxxxxxx 赠送数量为0')
      // } else {
      //   setDefaultNouponId(defaultShowFn.id)
      //   console.log('xxxxxxxxx 可继续赠送')
      // }
    })

    setBtnText('继续赠送')
    setShartVisible(false)
    return {
      title: message, // 分享卡片的title
      path: `/pages/get-gift/index?key=${shareCode}`, // 分享卡片的小程序路径
      imageUrl: shareBg // 分享卡片的图片链接
    }
  })

  return (
    <>
      <div className="continer">
        <div className="noupon">
          <div className="toptitle">
            <div className="daijinquan">代金券</div>
            <div className="change" onClick={() => setOpen(true)}>
              更换
            </div>
          </div>
          <div className="couponItem">
            <div className="leftItem">
              <div className="count">x{defaultShowNoupon.val.totalNum}</div>
              <div className="content">
                <div className="moneyCount">
                  {defaultShowNoupon.val.reducePrice / 100}
                  <span className="unit">元</span>
                </div>
                <div className="xianzhi">
                  {defaultShowNoupon.val.fullPrice / 100 > 0 ? `满${defaultShowNoupon.val.fullPrice / 100}可用` : '无门槛'}
                </div>
              </div>
            </div>
            <div className="rightItem">
              <div className="leftContentBox">
                <div className="title">{defaultShowNoupon.val.couponName}</div>
                <div className="time">有效期至{dayjs(defaultShowNoupon.val.endTime).format('YYYY.MM.DD')}</div>
                <div className="useTitle">全店可用</div>
              </div>
            </div>
          </div>
        </div>
        <div className="snedCount flex items-center">
          <div className="snedAllCount flex items-center">赠送代金券总数量</div>
          <div className="countInput flex items-center">
            <Input
              type="number"
              placeholder="填写张数 "
              className="inputComponent !flex items-center"
              style={{ fontSize: '20px', paddingRight: '5px' }}
              value={transferNum ?? ''}
              onChange={(e) => setTransferNumFn(e)}
            />
            <span>张</span>
          </div>
        </div>
        <div className="setting">
          <div className="title">领取设置</div>
          <Radio.Group value={radioValue} className="custom-color" onChange={(name) => setRadioValue(name)}>
            <Radio name="3">分享好友：一次性赠送多张</Radio>
            <Radio name="1">分享群聊：限每人领取1张</Radio>
            <Radio name="2">分享群聊：每人领取多张</Radio>
          </Radio.Group>
          {radioValue == '2' && (
            <div className="countInput_many">
              <Input
                type="number"
                placeholder="填写张数 "
                className="inputComponent"
                style={{ fontSize: '20px', paddingRight: '5px' }}
                value={limitReceiveNum}
                onChange={(e) => setLimitNumFn(e)}
              />
              <span>张</span>
            </div>
          )}
        </div>
        <div className="advice flex items-center">
          <div className="adviceTitle flex items-center">礼赠留言</div>
          <div className="adviceInput flex items-center">
            <Input
              placeholder="请输入"
              className="inputComponent !flex items-center"
              style={{ fontSize: '20px' }}
              value={message}
              onChange={(e) => setMessage(e.detail.value)}
            />
          </div>
        </div>
        <div className="beizhu">注：点击&quot;立即赠送&quot;即核减代金券张数，请谨慎操作 </div>
        <div
          className="sendBtn"
          onClick={() => {
            sendNoupon()
          }}
        >
          {btnText}
        </div>
      </div>

      <Popup open={open} placement="bottom" style={{ height: '70%' }}>
        <SelectConpou
          updateSelectConpouItem={updateSelectConpouItem}
          updateVisible={() => setOpen(false)}
          couponId={defaultShowNoupon.val.id}
          nouponList={nouponList}
        ></SelectConpou>
      </Popup>

      <Popup open={shartVisible} placement="bottom" style={{ height: '30%' }} rounded onClose={() => setShartVisible(false)}>
        <Popup.Close />
        <div className="shareTitle">
          <div className="text">分享</div>
        </div>
        <button open-type="share" className="shareBtn">
          <div className="icon"></div>
          <div className="text">微信好友</div>
        </button>
      </Popup>
    </>
  )
}

export default Index
